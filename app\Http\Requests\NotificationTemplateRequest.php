<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NotificationTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return auth()->user()->hasRole(["user", "sub_admin"]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            // 'placeholders' => 'nullable|array',
            // 'placeholders.*' => 'string|regex:/^[a-z_]+$/',
            'channels' => 'required|array',

            'translations.en.title' => 'required',
            'translations.en.short' => 'required',
            'translations.en.expanded' => 'required',

            'translations.es.title' => 'required',
            'translations.es.short' => 'required',
            'translations.es.expanded' => 'required',
        ];
        // check key is unique in create only
        if ($this->isMethod('post')) {
            $rules['key'] = 'required|unique:notification_templates,key';
            $rules['type'] = 'required|in:system,custom';
        }else{
            // $rules['key'] = 'required|unique:notification_templates,key,' . $this->route('notification-template') . ',id';
        }
        return $rules;
    }
}
