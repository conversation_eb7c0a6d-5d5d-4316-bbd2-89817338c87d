<?php

namespace App\Services;

class NotificationService
{
    public function sendNotification($user, $template, $notification)
    {
        if ($template && $template->is_active) {
            // Check each channel and send notifications accordingly
            if ($template->email) {
                $user->notify(new $notification($template, $user));
            }
            if ($template->app) {
                $user->notify(new $notification($template, $user));
            }
            if ($template->whatsapp) {
                $user->notify(new $notification($template, $user));
            }
            if ($template->sms) {
                $user->notify(new $notification($template, $user));
            }
            if ($template->push) {
                $user->notify(new $notification($template, $user));
            }
        }
    }

}
