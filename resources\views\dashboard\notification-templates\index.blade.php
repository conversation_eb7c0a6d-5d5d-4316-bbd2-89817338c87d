@extends('layouts.master')

@section('content')
    <div class="container-fluid">
        <div class="row">

            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="box-title">Notification Templates</h2>
                    <a class="btn btn_yellow" href="{{ route('notification-templates.create') }}">
                        <i class="icon-plus"></i>
                        Create Template
                    </a>
                </div>
            </div>

            <div class="col-md-12">
                <div class="white-box card_height_100 mb_30" style="margin-top: 20px;">
                    <div class="white_card_body">
                        <div class="table-responsive">
                            <table class="table custom_table">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Channal</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($templates as $template)
                                        <tr>
                                            <td>{{ $template->title }}</td>
                                            <td>
                                                <span
                                                    class="badge badge-{{ $template->type === 'system' ? 'primary' : 'secondary' }}">
                                                    {{ ucfirst($template->type) }}
                                                </span>
                                            </td>
                                            <td>
                                                @php
                                                    $channels = [];
                                                    if ($template->email == 1) $channels[] = 'Email';
                                                    if ($template->whatsapp == 1) $channels[] = 'WhatsApp';
                                                    if ($template->push == 1) $channels[] = 'Push';
                                                    if ($template->sms == 1) $channels[] = 'SMS';
                                                    if ($template->app == 1) $channels[] = 'App';
                                                @endphp

                                                {{ implode(', ', $channels) }}
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $template->is_active ? 'success' : 'danger' }}">
                                                    {{ $template->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </td>
                                            <td class="form_btn ">
                                                <div class="dropdown">
                                                    <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                    </button>
                                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                        <a href="{{ route('notification-templates.edit', $template->ids) }}"
                                                            class="dropdown-item">
                                                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                                                            Edit
                                                        </a>
                                                        @if ($template->type !== 'system')
                                                            <form
                                                                action="{{ route('notification-templates.destroy', $template->ids) }}"
                                                                method="POST" class="d-inline">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit"
                                                                    class="dropdown-item btn-sm delete-btn"
                                                                    onclick="return confirm('Are you sure?')">
                                                                    <i class="fa fa-trash"></i>
                                                                    Delete
                                                                </button>
                                                            </form>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="text-center">No templates found</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
