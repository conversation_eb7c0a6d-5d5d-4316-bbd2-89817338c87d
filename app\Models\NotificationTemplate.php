<?php

namespace App\Models;

use App\Traits\HasUuid;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Model;

class NotificationTemplate extends Model implements TranslatableContract
{
    use HasUuid, Translatable;

    protected $fillable = ['key', 'type', 'is_active', 'placeholders', 'whatsapp', 'email', 'push', 'sms', 'app'];
    public $translatedAttributes = ['title', "short", "expanded"];

    public function setPlaceholdersAttribute($value)
    {
        $value = array_map(function ($placeholder) {
            // Remove any existing wrapping of {{...}}
            $placeholder = trim($placeholder, '{}');
            return '{{' . str_replace(' ', '_', $placeholder) . '}}';
        }, array_unique($value));

        // Store the processed value as JSON
        $this->attributes['placeholders'] = json_encode($value);
    }


    public function getPlaceholdersAttribute($value)
    {
        return json_decode($value, true);
    }
}
